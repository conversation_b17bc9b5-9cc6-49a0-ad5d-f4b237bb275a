{"name": "giss-admin-ui", "version": "0.1.0", "private": true, "dependencies": {"@amcharts/amcharts4": "^4.10.38", "@amcharts/amcharts4-geodata": "^4.1.28", "@bryzos/giss-common-lib": "1.2.0", "@bryzos/giss-ui-library": "1.7.0", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@hookform/resolvers": "^3.0.0", "@mantine/hooks": "^7.7.1", "@material/circular-progress": "^14.0.0", "@mui/icons-material": "^5.0.0", "@mui/material": "^5.11.13", "@mui/x-date-pickers": "^6.19.0", "@tanstack/react-query": "^4.28.0", "@tanstack/react-query-devtools": "^4.28.0", "@types/jest": "^27.5.2", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "ag-grid-community": "^29.3.5", "ag-grid-react": "^29.3.5", "aws-amplify": "^5.0.20", "axios": "^1.3.4", "clsx": "^1.2.1", "cross-env": "^7.0.3", "dayjs": "^1.11.10", "immer": "^9.0.21", "leo-profanity": "^1.8.0", "lodash-es": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.43.7", "react-otp-input": "^3.0.0", "react-paginate": "^8.1.4", "react-router-dom": "^6.9.0", "react-sidebar": "^3.0.2", "react-simple-maps": "^3.0.0", "sass": "^1.59.3", "short-unique-id": "5.0.3", "truevault": "^1.3.1", "use-immer": "^0.8.1", "uuid": "^9.0.0", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yup": "^1.0.2"}, "scripts": {"dev": "npm run start", "start": "cross-env NODE_ENV=development npm run config-generate && vite --mode development", "build:qa": "cross-env NODE_ENV=qa npm run config-generate && vite build --mode qa", "build:staging": "cross-env NODE_ENV=staging npm run config-generate && vite build --mode staging", "build:demo": "cross-env NODE_ENV=demo npm run config-generate && vite build --mode demo", "build:production": "cross-env NODE_ENV=production npm run config-generate && vite build --mode production", "config-generate": "node ./scripts/configMove.mjs"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/lodash-es": "^4.17.7", "@types/node": "^20.12.7", "@types/react-sidebar": "^3.0.2", "@types/react-simple-maps": "^3.0.4", "@types/uuid": "^9.0.1", "@vitejs/plugin-react": "^4.0.4", "typescript": "^5.4.5", "vite": "^4.2.1", "vite-plugin-progress": "^0.0.7", "vite-plugin-svgr": "^3.2.0", "vite-tsconfig-paths": "^4.0.7"}}
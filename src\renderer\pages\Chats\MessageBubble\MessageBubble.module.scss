.messageBubble {
    padding: 8px 8px 8px 8px;
    border-radius: 6px;
    background-color: #000;
    color: #ffffff;
    max-width: 100%;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.24;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    min-width: 30px;

    a {
        color: inherit;
        text-decoration: none;
        cursor: pointer;
        
        &:hover {
            text-decoration: underline;
        }
    }
}

.othersMessage {
    background-color: #d4d4d4;
    color: #000;
}

// Role-based background colors
.buyerMessage {
    background-color: #e3f2fd; // Light blue for buyers
    color: #1565c0; // Darker blue text
    border-left: 3px solid #2196f3; // Blue accent border
}

.sellerMessage {
    background-color: #e8f5e8; // Light green for sellers
    color: #2e7d32; // Darker green text
    border-left: 3px solid #4caf50; // Green accent border
}

.adminMessage {
    background-color: #fff3e0; // Light orange for admin
    color: #ef6c00; // Darker orange text
    border-left: 3px solid #ff9800; // Orange accent border
}

:global(.MuiTooltip-popper) {
    .linkTooltip {
        background: #181e2b;
        color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 24px rgba(0,0,0,0.22);
        padding: 10px;
        max-width: 200px;
        min-width: 200px;
    }
}
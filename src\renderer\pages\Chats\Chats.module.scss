// Dropdown styles
.Dropdownpaper {
  max-height: 300px;
  overflow-y: auto;
}

.muiMenuList {
  padding: 0;
}

// Chat container styles
.messagesContainer {
  margin-top: 16px;
  max-height: 550px;
  overflow-y: auto;
  padding: 16px;
  background-color: #eee;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Message wrapper - main container for each message
.messageWrapper {
  display: flex;
  flex-direction: column;
  max-width: 100%;
  position: relative;
  padding:6px 12px;
  border-radius: 10px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.02);
  }

  &.myMessageWrapper {
    align-self: flex-end;
    align-items: flex-end;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05), rgba(0, 123, 255, 0.02));

    &:hover {
      background: linear-gradient(135deg, rgba(0, 123, 255, 0.08), rgba(0, 123, 255, 0.04));
    }
  }

  &.othersMessageWrapper {
    align-self: flex-start;
    align-items: flex-start;
    background: rgba(248, 249, 250, 0.5);

    &:hover {
      background: rgba(248, 249, 250, 0.8);
    }
  }
}

.messageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  padding: 0 4px;
  min-height: 20px;
  width: 100%;
}

.messageTimestamp {
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
}

.menuButton {
  padding: 4px !important;
  color: #6c757d !important;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04) !important;
    color: #495057 !important;
  }

  .MuiSvgIcon-root {
    font-size: 16px;
  }
}

.messageContent {
  position: relative;
  margin-bottom: 4px;

  &.myMessageContent {
    align-self: flex-end;
  }

  &.othersMessageContent {
    align-self: flex-start;
  }
}

.textMessageWrapper {
  display: flex;
  flex-direction: column;
}

.attachmentWrapper {
  display: flex;
  flex-direction: column;
}

.messageFooter {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  min-height: 16px;
  padding: 0 4px;
}

.messageStatus {
  font-size: 10px;
  color: #adb5bd;
  display: flex;
  align-items: center;
  gap: 4px;
}

.messageInputContainer {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 12px;
  margin-top: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.formattingToolbar {
  display: flex;
  gap: 4px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 12px;

  .MuiIconButton-root {
    padding: 6px;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8f9fa;
      transform: translateY(-1px);
    }

    .MuiSvgIcon-root {
      font-size: 18px;
      color: #495057;
    }
  }
}

.contentEditable {
  min-height: 60px;
  max-height: 120px;
  overflow-y: auto;
  padding: 12px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background-color: #fff;
  font-family: Noto Sans;
  font-size: 14px;
  line-height: 1.5;
  outline: none;
  resize: none;

  &:focus {
    border-color: var(--primaryColor);
  }

  &:empty::before {
    content: attr(placeholder);
    color: #6c757d;
    pointer-events: none;
  }

  // Formatting styles within the editor
  strong, b {
    font-weight: bold;
  }

  em, i {
    font-style: italic;
  }

  u {
    text-decoration: underline;
  }
}

.sendButton {
  background: var(--primaryColor);
  color: white;
  border: none;
  font-family: Noto Sans;
  border-radius: 8px;
  padding: 10px 25px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 12px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);

  &:disabled {
    cursor: not-allowed;
    box-shadow: none;
    opacity: 0.6;
  }
}